<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>star-framework</artifactId>
        <groupId>com.demo</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>star-spring-boot-starter-biz-tenant</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>多租户</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-common</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.demo</groupId>
            <artifactId>star-spring-boot-starter-mq</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

</project>
